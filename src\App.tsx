// App.tsx
import React, { useState } from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import HomePage from './HomePage';
import CandidatesPage from './CandidatePage';
import VotersPage from './VotersPage';
import PositionsPage from './PositionsPage';
import ResultPage from './ResultPage';
import Sidebar from './Sidebar';
import Header from './Header';
import AdminLoginPage from './AdminLoginPage';
import UserLoginPage from './UserLoginPage';
import VotePage from './VotePage'; // Import the user voting page
import HomePageUser from './HomePageUser';
import ResultPageUser from './ResultPageUser';
import 'bootstrap/dist/css/bootstrap.min.css';
import './index.css';

/**
 * MainLayout component
 * This component wraps all the main pages, providing the sidebar and header.
 * This ensures a consistent layout for all authenticated views.
 */
const MainLayout: React.FC<{ onLogout: () => void; children: React.ReactNode }> = ({ onLogout, children }) => {
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    if (isSidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <div className="App">
      <div
        className={`overlay ${isSidebarOpen ? 'show' : ''}`}
        onClick={closeSidebar}
      ></div>
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isOpen={isSidebarOpen} closeSidebar={closeSidebar} onLogout={onLogout} />
      <main className={isSidebarOpen ? 'content-shifted' : ''}>
        {children}
      </main>
    </div>
  );
};

/**
 * UserLayout component
 * Simple layout for user dashboard without sidebar
 */
const UserLayout: React.FC<{ onLogout: () => void; children: React.ReactNode }> = ({ onLogout, children }) => {
  return (
    <div className="App">
      <Header toggleSidebar={() => {}} onLogout={onLogout} />
      <main>
        {children}
      </main>
    </div>
  );
};

/**
 * Main App component
 * Handles routing and the distinction between public (login) and private (main app) routes.
 */
const App: React.FC = () => {
  const [isAdminLoggedIn, setIsAdminLoggedIn] = useState(() => {
    return localStorage.getItem('isAdminLoggedIn') === 'true';
  });
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(() => {
    return localStorage.getItem('isUserLoggedIn') === 'true';
  });

  const handleAdminLogin = () => {
    console.log('handleAdminLogin called');
    localStorage.setItem('isAdminLoggedIn', 'true');
    setIsAdminLoggedIn(true);
  };

  const handleUserLogin = (schoolId: string) => {
    localStorage.setItem('isUserLoggedIn', 'true');
    setIsUserLoggedIn(true);
    localStorage.setItem('currentUserSchoolId', schoolId);
  };

  const handleAdminLogout = () => {
    localStorage.removeItem('isAdminLoggedIn');
    setIsAdminLoggedIn(false);
  };

  const handleUserLogout = () => {
    localStorage.removeItem('isUserLoggedIn');
    localStorage.removeItem('currentUserSchoolId');
    setIsUserLoggedIn(false);
  };

  return (
    <Router>
      <Routes>
        {/* Login routes */}
        <Route path="/admin-login" element={<AdminLoginPage onLogin={handleAdminLogin} />} />
        <Route path="/user-login" element={<UserLoginPage onUserLogin={handleUserLogin} />} />
        <Route path="/" element={<Navigate to="/user-login" />} />
        
        {/* Admin routes - no /admin prefix */}
        {isAdminLoggedIn ? (
          <>
            <Route path="/home" element={<MainLayout onLogout={handleAdminLogout}><HomePage /></MainLayout>} />
            <Route path="/positions" element={<MainLayout onLogout={handleAdminLogout}><PositionsPage /></MainLayout>} />
            <Route path="/candidates" element={<MainLayout onLogout={handleAdminLogout}><CandidatesPage /></MainLayout>} />
            <Route path="/voters" element={<MainLayout onLogout={handleAdminLogout}><VotersPage /></MainLayout>} />
            <Route path="/results" element={<MainLayout onLogout={handleAdminLogout}><ResultPage /></MainLayout>} />
          </>
        ) : (
          <Route path="/*" element={<Navigate to="/admin-login" />} />
        )}
        
        {/* User routes - with /user prefix */}
        {isUserLoggedIn ? (
          <>
            <Route path="/user" element={<UserLayout onLogout={handleUserLogout}><HomePageUser /></UserLayout>} />
            <Route path="/user/results" element={<UserLayout onLogout={handleUserLogout}><ResultPageUser /></UserLayout>} />
          </>
        ) : (
          <Route path="/user/*" element={<Navigate to="/user-login" />} />
        )}
      </Routes>
    </Router>
  );
};

export default App;
