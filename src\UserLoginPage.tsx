import React, { useState } from 'react';
import { Form, Button, Alert, InputGroup } from 'react-bootstrap';
import { FaUser, FaLock } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import './UserLoginPage.css';

interface UserLoginPageProps {
  onUserLogin: (schoolId: string) => void;
}

const UserLoginPage: React.FC<UserLoginPageProps> = ({ onUserLogin }) => {
  const [schoolId, setSchoolId] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();

    const voters = JSON.parse(localStorage.getItem('voters') || '[]');
    const found = voters.find((v: any) => v.schoolId === schoolId.trim());
    if (found && password === 'password') {
      localStorage.setItem('currentUserSchoolId', found.schoolId);
      localStorage.setItem('isUserLoggedIn', 'true');
      setError('');
      onUserLogin(found.schoolId);
      return;
    }

    setError('Invalid School ID or password.');
  };

  return (
    <div className="login-page">
      <div className="login-card">
        <h2 className="text-center mb-4 fw-bold">User Login</h2>
        <Form onSubmit={handleLogin}>
          {error && <Alert variant="danger">{error}</Alert>}

          <Form.Group className="mb-3" controlId="schoolId">
            <InputGroup>
              <InputGroup.Text>
                <FaUser />
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="School ID"
                value={schoolId}
                onChange={(e) => setSchoolId(e.target.value)}
                required
              />
            </InputGroup>
          </Form.Group>

          <Form.Group className="mb-4" controlId="password">
            <InputGroup>
              <InputGroup.Text>
                <FaLock />
              </InputGroup.Text>
              <Form.Control
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </InputGroup>
          </Form.Group>

          <Button variant="primary" type="submit" className="w-100 login-button">
            Login
          </Button>
        </Form>
        <div className="text-center mt-3">
          <small className="text-light">
            Use your <b>School ID</b> and password <b>password</b> to login
          </small>
          <br />
          <Link to="/admin-login" style={{ color: "#0d6efd" }}>
            Login as Admin
          </Link>
        </div>
      </div>
    </div>
  );
};

export default UserLoginPage;
