@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;800&display=swap');

:root {
  --blue-primary: #1877f2;
  --blue-light: #e7f3ff;
  --blue-dark: #145dbf;
  --white: #ffffff;
  --gray-light: #f5f8ff;
  --gray-medium: #dbe9ff;
  --sidebar-width: 280px;
  --transition-time: 0.3s;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--gray-light);
  color: #222;
  margin: 0;
  overflow-x: hidden;
}

/* Sidebar */
#sidebar {
  width: 220px;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background: var(--blue-primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  padding: 1.5rem 1rem;
  overflow-y: auto;
  transition: transform 0.3s ease;
  z-index: 1000;
  transform: translateX(-100%);
}
#sidebar.show {
  transform: translateX(0);
}
#sidebar .nav-link {
  color: var(--white);
  font-weight: 600;
  margin: 0.5rem 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  transition: background-color 0.2s;
  text-decoration: none;
}
#sidebar .nav-link:hover,
#sidebar .nav-link.active {
  background-color: var(--blue-dark);
}
#sidebar .material-icons {
  font-size: 24px;
  user-select: none;
}

/* Main content */
#content {
  margin-left: 220px;
  padding: 2rem 2rem 4rem;
  transition: margin-left var(--transition-time) ease;
  width: 100%;
}
@media (max-width: 768px) {
  #sidebar {
    transform: translateX(-100%);
  }
  #sidebar.show {
    transform: translateX(0);
  }
  #content {
    margin-left: 0;
  }
}

/* Hamburger */
#hamburger {
  position: fixed;
  top: 16px;
  left: 16px;
  z-index: 1100;
  width: 44px;
  height: 44px;
  background: var(--blue-primary);
  border: none;
  border-radius: 8px;
  color: var(--white);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(24, 119, 242, 0.4);
  transition: background-color 0.3s;
}
#hamburger:hover {
  background: var(--blue-dark);
}

/* Announcement cards */
.announcement-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgb(24 119 242 / 0.14);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}
.announcement-card h3 {
  color: var(--blue-dark);
  margin-bottom: 0.5rem;
}

/* Buttons */
.btn-custom-blue {
  background-color: #4a90e2;
  color: white;
  border: none;
}
.btn-custom-blue:hover {
  background-color: #357ab8;
}

/* Table */
.table-header-custom {
  background-color: #e7f1fd;
  font-weight: bold;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #0d6efd, #17a2b8);
  color: white;
  padding: 5rem 1.5rem;
  border-radius: 1rem;
  margin-top: 2rem;
  margin-bottom: 3rem;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
.hero-section h1 {
  font-weight: 800;
}
.hero-section .lead {
  font-size: 1.25rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Section Title */
.section-title {
  font-weight: 700;
  color: #343a40;
  margin-bottom: 2.5rem;
}

/* Countdown Card */
.countdown-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  text-align: center;
}
.countdown-card .display-4 {
  font-weight: 700;
  color: #0d6efd;
}

/* Feature Card */
.feature-card {
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 1rem;
  height: 100%;
}
.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}
.feature-card .card-title {
  font-weight: 700;
  color: #0d6efd;
  display: flex;
  align-items: center;
}
.feature-card .card-title i {
  font-size: 1.75rem;
  margin-right: 1rem;
  width: 30px;
}
.feature-card .list-group-item {
  border: none;
  padding-left: 0;
}
