export type LoginResult =
  | { type: 'admin' }
  | { type: 'user'; user: { schoolId: string; name: string } }
  | { type: 'error'; message: string };

export function login(username: string, password: string): LoginResult {
  // Admin credentials
  if (username === 'admin' && password === 'password') {
    return { type: 'admin' };
  }

  // User credentials (schoolId from voter list)
  const voters = JSON.parse(localStorage.getItem('voters') || '[]');
  const user = voters.find((v: any) => v.schoolId === username.trim());
  if (user && password === 'password') {
    return { type: 'user', user };
  }

  return { type: 'error', message: 'Invalid username or password.' };
}