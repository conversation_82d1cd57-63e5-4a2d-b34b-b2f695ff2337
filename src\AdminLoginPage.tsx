import React, { useState } from 'react';
import { Form, Button, Alert, InputGroup } from 'react-bootstrap';
import { FaUser, FaLock } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import './AdminLoginPage.css'; // Import the new CSS file

interface AdminLoginPageProps {
  onLogin: () => void;
}

const AdminLoginPage: React.FC<AdminLoginPageProps> = ({ onLogin }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Login attempt - Username:', `"${username}"`, 'Password:', `"${password}"`);
    console.log('Username length:', username.length, 'Password length:', password.length);
    
    if (username.trim() === 'admin' && password.trim() === 'password') {
      console.log('Login successful!');
      setError('');
      onLogin();
    } else {
      console.log('Login failed!');
      setError('Invalid username or password.');
    }
  };

  return (
    <div className="login-page">
      <div className="login-card">
        <h2 className="text-center mb-4 fw-bold">Admin</h2>
        <Form onSubmit={handleLogin}>
          {error && <Alert variant="danger">{error}</Alert>}

          <Form.Group className="mb-3" controlId="username">
            <InputGroup>
              <InputGroup.Text>
                <FaUser />
              </InputGroup.Text>
              <Form.Control
                type="text"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
            </InputGroup>
          </Form.Group>

          <Form.Group className="mb-4" controlId="password">
            <InputGroup>
              <InputGroup.Text>
                <FaLock />
              </InputGroup.Text>
              <Form.Control
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </InputGroup>
          </Form.Group>

          <Button variant="primary" type="submit" className="w-100 login-button">
            Login
          </Button>
        </Form>
        <div className="text-center mt-3">
          <small className="text-light">Use admin / password to login</small>
          <br />
          <Link to="/user-login" style={{ color: "#0d6efd" }}>
            Login as User
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminLoginPage;
