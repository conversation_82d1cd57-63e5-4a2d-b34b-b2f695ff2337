/* Sidebar.css 
:root {
  --sidebar-bg: #212529;
  --sidebar-header-bg: #1a1d20;
  --text-color: #ced4da;
  --text-color-hover: #ffffff;
  --link-active-bg: #0d6efd;
  --border-color: #343a40;
  --sidebar-width: 260px;
}

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--sidebar-bg);
  position: fixed;
  top: 0;
  left: calc(-1 * var(--sidebar-width)); /* Hidden by default */
  height: 100%;
  transition: left 0.3s ease-in-out;
  z-index: 1050; /* Higher than overlay */
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.2);
}

.sidebar.open {
  left: 0;
}

/* --- Overlay --- */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
  z-index: 1040; /* Below the sidebar */
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* --- Header --- */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 15px; /* Space between icon and title */
  padding: 20px;
  background-color: var(--sidebar-header-bg);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color-hover);
}

.sidebar-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

/* --- Navigation --- */
.sidebar-nav {
  list-style: none;
  padding: 10px 0;
  margin: 0;
  flex-grow: 1;
  overflow-y: auto;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 20px;
  margin: 5px 10px;
  border-radius: 8px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.2s, color 0.2s;
  border-left: 4px solid transparent; /* Placeholder for active state */
}

.nav-link:hover {
  background-color: var(--border-color);
  color: var(--text-color-hover);
}

.nav-link.active {
  background-color: rgba(13, 110, 253, 0.15); /* Subtle active background */
  color: var(--text-color-hover);
  font-weight: 500;
  border-left: 4px solid var(--link-active-bg);
}

.nav-icon {
  display: inline-flex;
  align-items: center;
  font-size: 1.1rem;
}

/* --- Footer --- */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

.logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 1rem;
  font-weight: 500;
}