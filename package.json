{"name": "capstone-final", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"bootstrap": "^5.3.7", "chart.js": "^4.5.0", "lucide-react": "^0.525.0", "material-icons": "^1.13.14", "react-bootstrap": "^2.10.10", "react-bootstrap-icons": "^1.11.6", "react-chartjs-2": "^5.3.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}